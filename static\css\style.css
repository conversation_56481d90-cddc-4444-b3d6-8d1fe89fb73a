/* 德国瑞士设计风格 */
:root {
    --red: #FF0000;
    --black: #000000;
    --white: #FFFFFF;
    --gray: #E5E5E5;
    --light-gray: #F5F5F5;
    --dark-gray: #999999;
    --green: #00AA00;
    --grid: 8px;
    --font-sans: 'Helvetica Neue', Arial, sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    background: var(--white);
    color: var(--black);
    line-height: 1.5;
    letter-spacing: -0.02em;
}

.container {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    padding: calc(var(--grid) * 4);
}

/* 标题区 */
header {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    margin-bottom: calc(var(--grid) * 4);
    border-bottom: 2px solid var(--black);
    padding-bottom: calc(var(--grid) * 4);
    gap: calc(var(--grid) * 4);
}

.header-right {
    display: flex;
    align-items: center;
    gap: calc(var(--grid) * 2);
}

/* 搜索框样式 */
.search-container {
    display: flex;
    align-items: center;
    background: var(--gray);
    border: 2px solid var(--black);
    padding: calc(var(--grid));
    margin-left: calc(var(--grid) * 2);
}

.search-input {
    border: none;
    background: transparent;
    padding: calc(var(--grid));
    font-family: var(--font-sans);
    font-size: 14px;
    width: 200px;
    outline: none;
}

.search-btn {
    background: transparent;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: calc(var(--grid));
}

/* 主题切换按钮 */
.theme-toggle {
    background: var(--white);
    border: 2px solid var(--black);
    width: 48px;
    height: 48px;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s;
}

.theme-toggle:hover {
    background: var(--black);
    color: var(--white);
}

/* 统计仪表板 */
.stats-dashboard {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: calc(var(--grid) * 3);
    margin-bottom: calc(var(--grid) * 6);
    padding: calc(var(--grid) * 4);
    background: var(--light-gray);
    border: 2px solid var(--black);
}

.stats-card {
    background: var(--white);
    border: 2px solid var(--black);
    padding: calc(var(--grid) * 3);
    text-align: center;
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--red);
    margin-bottom: calc(var(--grid));
}

.stats-label {
    font-size: 12px;
    text-transform: uppercase;
    font-weight: 500;
    color: var(--dark-gray);
}

.progress-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray);
    border: 1px solid var(--black);
    margin-bottom: calc(var(--grid));
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--green);
    transition: width 0.3s ease;
    width: 0%;
}

h1 {
    font-size: 48px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: -0.04em;
}

.btn-primary {
    background: var(--red);
    color: var(--white);
    border: none;
    padding: calc(var(--grid) * 2) calc(var(--grid) * 4);
    font-size: 16px;
    font-weight: 500;
    text-transform: uppercase;
    cursor: pointer;
    transition: transform 0.2s;
}

.btn-primary:hover {
    transform: translateY(-2px);
}

/* 侧边栏样式 - 简化版 */
.sidebar {
    position: fixed;
    top: 0;
    left: -280px;
    width: 280px;
    height: 100vh;
    background: var(--light-gray);
    border-right: 3px solid var(--black);
    transition: left 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
}

.sidebar.active {
    left: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: calc(var(--grid) * 3);
    background: var(--white);
    border-bottom: 2px solid var(--black);
}

.sidebar-header h3 {
    font-size: 20px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: -0.02em;
}

.close-btn {
    background: var(--black);
    color: var(--white);
    border: none;
    width: 28px;
    height: 28px;
    font-size: 18px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.2s;
}

.close-btn:hover {
    background: var(--red);
}

.sidebar-content {
    padding: calc(var(--grid) * 3);
}

.sidebar-section {
    margin-bottom: calc(var(--grid) * 4);
    background: var(--white);
    border: 2px solid var(--black);
    padding: calc(var(--grid) * 3);
}

.sidebar-section h4 {
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: calc(var(--grid) * 3);
    color: var(--black);
    border-bottom: 1px solid var(--gray);
    padding-bottom: calc(var(--grid));
}

.tag-list {
    display: flex;
    flex-direction: column;
    gap: calc(var(--grid));
}

.tag-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: calc(var(--grid) * 1.5);
    background: var(--light-gray);
    border: 1px solid var(--gray);
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    font-weight: 500;
}

.tag-item:hover {
    background: var(--black);
    color: var(--white);
}

.tag-count {
    background: var(--red);
    color: var(--white);
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
}

.add-tag-btn {
    width: 100%;
    padding: calc(var(--grid) * 2);
    background: var(--white);
    border: 2px solid var(--black);
    color: var(--black);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    margin-top: calc(var(--grid) * 2);
}

.add-tag-btn:hover {
    background: var(--black);
    color: var(--white);
}

/* 主内容区域 */
.main-content {
    margin-left: 0;
    width: 100%;
}

/* 侧边栏覆盖层 */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* 工具栏样式 */
.toolbar {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: calc(var(--grid) * 4);
    margin-bottom: calc(var(--grid) * 4);
    padding: calc(var(--grid) * 3);
    background: var(--gray);
    border: 2px solid var(--black);
}

.toolbar-left, .toolbar-center, .toolbar-right {
    display: flex;
    align-items: center;
    gap: calc(var(--grid) * 2);
}

.toolbar-center {
    justify-content: center;
}

.sidebar-toggle {
    background: var(--white);
    border: 2px solid var(--black);
    padding: calc(var(--grid) * 2);
    font-size: 18px;
    cursor: pointer;
    transition: all 0.2s;
}

.sidebar-toggle:hover {
    background: var(--black);
    color: var(--white);
}

/* 旧的批量操作按钮样式已移除 */

.filter-group {
    display: flex;
    gap: calc(var(--grid) * 2);
}

.filter-btn {
    background: var(--white);
    border: 2px solid var(--black);
    padding: calc(var(--grid) * 1.5) calc(var(--grid) * 3);
    font-weight: 500;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s;
}

.filter-btn.active {
    background: var(--black);
    color: var(--white);
}

select {
    width: 100%;
    padding: calc(var(--grid) * 1.5);
    border: 2px solid var(--black);
    font-family: var(--font-sans);
    font-size: 14px;
    text-transform: uppercase;
    cursor: pointer;
    background: var(--white);
}

/* 备忘录网格 */
.memo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: calc(var(--grid) * 3);
}

/* 日历视图样式 */
.calendar-view {
    margin-bottom: calc(var(--grid) * 4);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: calc(var(--grid) * 4);
    padding: calc(var(--grid) * 3);
    background: var(--gray);
    border: 2px solid var(--black);
}

.calendar-header button {
    background: var(--white);
    border: 2px solid var(--black);
    padding: calc(var(--grid) * 2);
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s;
}

.calendar-header button:hover {
    background: var(--black);
    color: var(--white);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    background: var(--black);
    border: 2px solid var(--black);
}

.calendar-day {
    background: var(--white);
    padding: calc(var(--grid) * 2);
    min-height: 80px;
    display: flex;
    flex-direction: column;
    position: relative;
}

.calendar-day.other-month {
    background: var(--light-gray);
    color: var(--dark-gray);
}

.calendar-day.today {
    background: var(--red);
    color: var(--white);
}

.calendar-day-number {
    font-weight: 700;
    margin-bottom: calc(var(--grid));
}

.calendar-memo {
    font-size: 10px;
    background: var(--green);
    color: var(--white);
    padding: 2px 4px;
    margin-bottom: 2px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

/* 批量操作功能已移除 */

/* 备忘录卡片 */
.memo-card {
    position: relative;
    background: var(--white);
    border: 2px solid var(--black);
    padding: calc(var(--grid) * 3);
    transition: all 0.3s ease;
    display: grid;
    gap: calc(var(--grid) * 2);
}

.memo-favorite {
    position: absolute;
    top: calc(var(--grid) * 2);
    left: calc(var(--grid) * 2);
}

.favorite-btn {
    background: transparent;
    border: none;
    font-size: 20px;
    cursor: pointer;
    opacity: 0.3;
    transition: all 0.2s;
}

.favorite-btn:hover, .favorite-btn.active {
    opacity: 1;
    transform: scale(1.2);
}

/* 标签样式 */
.memo-tags {
    display: flex;
    gap: calc(var(--grid));
    flex-wrap: wrap;
    margin: calc(var(--grid) * 2) 0;
}

.tag {
    background: var(--red);
    color: var(--white);
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    border-radius: 0;
}

/* 分享按钮样式已移除 */

/* 批量选择功能已移除 */

.memo-status {
    position: absolute;
    top: calc(var(--grid) * 2);
    right: calc(var(--grid) * 2);
}

.status-checkbox {
    width: 24px;
    height: 24px;
    border: 2px solid var(--black);
    cursor: pointer;
    transition: all 0.2s ease;
    appearance: none;
    position: relative;
    background: var(--white);
}

.status-checkbox:hover {
    border-color: var(--green);
    transform: scale(1.1);
}

.status-checkbox:checked {
    background: var(--green);
    border-color: var(--green);
}

.status-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 16px;
    font-weight: bold;
}

/* 已完成状态的卡片样式 */
.memo-card.completed {
    background: var(--light-gray);
    border-color: var(--dark-gray);
    opacity: 0.8;
    transform: scale(0.98);
}

.memo-card.completed h3 {
    text-decoration: line-through;
    color: var(--dark-gray);
}

.memo-card.completed p {
    color: var(--dark-gray);
}

.memo-card.completed .memo-meta {
    color: var(--dark-gray);
}

/* 撤回按钮样式 */
.undo-btn {
    background: var(--green) !important;
    color: var(--white) !important;
    border-color: var(--green) !important;
}

.undo-btn:hover {
    background: var(--white) !important;
    color: var(--green) !important;
}

.memo-card:hover {
    transform: translateY(-4px);
}

.memo-card h3 {
    font-size: 24px;
    margin-bottom: calc(var(--grid) * 2);
    font-weight: 700;
    text-transform: uppercase;
}

.memo-card p {
    margin-bottom: calc(var(--grid) * 2);
    font-size: 16px;
}

.memo-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--grid);
    margin-top: calc(var(--grid) * 2);
    font-size: 12px;
    text-transform: uppercase;
}

.memo-actions {
    display: flex;
    gap: var(--grid);
    margin-top: calc(var(--grid) * 2);
}

.memo-actions button {
    flex: 1;
    padding: calc(var(--grid));
    border: 1px solid var(--black);
    background: var(--white);
    font-size: 12px;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s;
}

.memo-actions button:hover {
    background: var(--black);
    color: var(--white);
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    padding: calc(var(--grid) * 2);
    overflow-y: auto;
}

.modal.active {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 5vh;
}

.modal-content {
    background: var(--white);
    padding: calc(var(--grid) * 4);
    width: 100%;
    max-width: 500px;
    border: 2px solid var(--black);
    margin: auto;
}

.modal-content h2 {
    font-size: 32px;
    margin-bottom: calc(var(--grid) * 4);
    text-transform: uppercase;
    font-weight: 700;
}

/* 标签选择样式 - 简化版 */
#tags {
    width: 100%;
    padding: calc(var(--grid) * 1.5);
    border: 2px solid var(--black);
    font-family: var(--font-sans);
    font-size: 16px;
    background: var(--white);
    cursor: pointer;
}

/* 分享功能已移除 */

/* 标签管理模态框样式 */
.tag-input-container {
    display: flex;
    gap: calc(var(--grid) * 2);
    align-items: center;
}

.tag-input-container input {
    flex: 1;
}

.existing-tags-section {
    margin-top: calc(var(--grid) * 4);
}

.existing-tags-section h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: calc(var(--grid) * 3);
    text-transform: uppercase;
}

.tags-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: calc(var(--grid) * 2);
    max-height: 200px;
    overflow-y: auto;
}

.tag-management-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: calc(var(--grid) * 2);
    background: var(--light-gray);
    border: 2px solid var(--gray);
    transition: all 0.2s;
}

.tag-management-item:hover {
    border-color: var(--black);
}

.tag-name {
    font-weight: 500;
    font-size: 14px;
}

.delete-tag-btn {
    background: var(--red);
    color: var(--white);
    border: none;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.delete-tag-btn:hover {
    background: var(--black);
}

/* 设置模态框样式 */
.settings-section {
    margin-bottom: calc(var(--grid) * 4);
    padding-bottom: calc(var(--grid) * 4);
    border-bottom: 1px solid var(--gray);
}

.settings-section h3 {
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: calc(var(--grid) * 3);
    color: var(--red);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: calc(var(--grid) * 2);
    padding: calc(var(--grid) * 2);
    background: var(--light-gray);
}

.setting-item label {
    margin: 0;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: calc(var(--grid));
}

.setting-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    border: 2px solid var(--black);
    appearance: none;
    cursor: pointer;
    position: relative;
    background: var(--white);
}

.setting-item input[type="checkbox"]:checked {
    background: var(--green);
    border-color: var(--green);
}

.setting-item input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

/* 表单样式 */
.form-group {
    margin-bottom: calc(var(--grid) * 3);
}

label {
    display: block;
    margin-bottom: var(--grid);
    font-weight: 500;
    text-transform: uppercase;
    font-size: 14px;
}

input[type="text"],
input[type="date"],
textarea {
    width: 100%;
    padding: calc(var(--grid) * 1.5);
    border: 2px solid var(--black);
    font-family: var(--font-sans);
    font-size: 16px;
}

input[type="color"] {
    width: 100%;
    height: calc(var(--grid) * 5);
    border: 2px solid var(--black);
    padding: 0;
}

.form-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: calc(var(--grid) * 2);
    margin-top: calc(var(--grid) * 4);
}

.btn-secondary {
    background: var(--white);
    color: var(--black);
    border: 2px solid var(--black);
    padding: calc(var(--grid) * 2) calc(var(--grid) * 4);
    font-size: 16px;
    font-weight: 500;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-secondary:hover {
    background: var(--black);
    color: var(--white);
}

/* 添加加载和错误状态样式 */
.loading, .error {
    text-align: center;
    padding: calc(var(--grid) * 8);
    font-size: 18px;
}

.error {
    color: var(--red);
}

.loading {
    color: var(--gray);
}

/* 改进表单验证样式 */
.form-group input:invalid {
    border-color: var(--red);
}

.form-group input:valid {
    border-color: var(--gray);
}

/* 通知系统样式 - 简洁版本 */
.notification {
    position: fixed;
    top: 30px;
    right: 30px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    z-index: 1000;
    transform: scale(0) rotate(180deg);
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    opacity: 0;
}

.notification.show {
    transform: scale(1) rotate(0deg);
    opacity: 1;
}

.notification-success {
    background: var(--green);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(0, 170, 0, 0.3);
}

.notification-error {
    background: var(--red);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(255, 0, 0, 0.3);
}

.notification-info {
    background: var(--black);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 浮动操作按钮 */
.fab-container {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.main-fab {
    background: var(--red);
    color: var(--white);
    transform: rotate(0deg);
}

/* 浮动按钮旋转效果 */
.main-fab.active {
    transform: rotate(45deg);
}

/* 浮动按钮菜单样式 */
.fab-menu {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: calc(var(--grid) * 2);
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.fab-menu.active {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.sub-fab {
    background: var(--white);
    color: var(--black);
    border: 2px solid var(--black);
    font-size: 18px;
    width: 48px;
    height: 48px;
}

.sub-fab:hover {
    background: var(--black);
    color: var(--white);
    transform: scale(1.1);
}

/* 快捷键提示 */
.shortcuts-hint {
    position: fixed;
    bottom: 30px;
    left: 30px;
    background: var(--black);
    color: var(--white);
    padding: calc(var(--grid) * 3);
    border-radius: 8px;
    font-size: 12px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
    pointer-events: none;
}

.shortcuts-hint.show {
    opacity: 1;
    transform: translateY(0);
}

.shortcut-item {
    margin-bottom: calc(var(--grid));
    font-family: monospace;
}

.shortcut-item:last-child {
    margin-bottom: 0;
}

/* 列表视图样式已移除 */

/* 暗色主题 */
[data-theme="dark"] {
    --white: #1a1a1a;
    --black: #ffffff;
    --gray: #2a2a2a;
    --light-gray: #333333;
    --dark-gray: #cccccc;
    --red: #ff4444;
    --green: #44aa44;
}

[data-theme="dark"] body {
    background: var(--white);
    color: var(--black);
}

[data-theme="dark"] .stats-dashboard {
    background: var(--gray);
    border-color: var(--black);
}

[data-theme="dark"] .stats-card {
    background: var(--white);
    border-color: var(--black);
    color: var(--black);
}

[data-theme="dark"] .stats-number {
    color: var(--red);
}

[data-theme="dark"] .sidebar {
    background: var(--gray);
    border-color: var(--black);
}

[data-theme="dark"] .sidebar-header {
    background: var(--white);
    border-color: var(--black);
}

[data-theme="dark"] .sidebar-section {
    background: var(--white);
    border-color: var(--black);
}

[data-theme="dark"] .tag-item {
    background: var(--light-gray);
    border-color: var(--dark-gray);
    color: var(--black);
}

[data-theme="dark"] .tag-item:hover {
    background: var(--black);
    color: var(--white);
}

[data-theme="dark"] .add-tag-btn {
    background: var(--white);
    border-color: var(--black);
    color: var(--black);
}

[data-theme="dark"] .add-tag-btn:hover {
    background: var(--black);
    color: var(--white);
}

[data-theme="dark"] .toolbar {
    background: var(--gray);
    border-color: var(--black);
}

[data-theme="dark"] .search-container {
    background: var(--light-gray);
    border-color: var(--black);
}

[data-theme="dark"] .search-input {
    color: var(--black);
}

[data-theme="dark"] .sidebar-toggle,
[data-theme="dark"] .filter-btn,
[data-theme="dark"] .filter-select,
[data-theme="dark"] .bulk-btn {
    background: var(--white);
    border-color: var(--black);
    color: var(--black);
}

[data-theme="dark"] .filter-btn.active {
    background: var(--black);
    color: var(--white);
}

/* 旧的批量按钮样式已移除 */

[data-theme="dark"] .memo-card {
    background: var(--white);
    border-color: var(--black);
    color: var(--black);
}

[data-theme="dark"] .memo-actions button {
    background: var(--white);
    border-color: var(--black);
    color: var(--black);
}

[data-theme="dark"] .memo-actions button:hover {
    background: var(--black);
    color: var(--white);
}

[data-theme="dark"] .modal-content {
    background: var(--white);
    border-color: var(--black);
    color: var(--black);
}

[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select {
    background: var(--white);
    border-color: var(--black);
    color: var(--black);
}

[data-theme="dark"] .calendar-view {
    background: var(--white);
}

[data-theme="dark"] .calendar-header {
    background: var(--gray);
    border-color: var(--black);
}

[data-theme="dark"] .calendar-day {
    background: var(--white);
    color: var(--black);
}

[data-theme="dark"] .calendar-day.other-month {
    background: var(--gray);
    color: var(--dark-gray);
}

[data-theme="dark"] .calendar-day.today {
    background: var(--red);
    color: var(--white);
}

[data-theme="dark"] .theme-toggle {
    background: var(--white);
    border-color: var(--black);
    color: var(--black);
}

[data-theme="dark"] .theme-toggle:hover {
    background: var(--black);
    color: var(--white);
}

[data-theme="dark"] .btn-primary {
    background: var(--red);
    color: var(--white);
}

[data-theme="dark"] .btn-secondary {
    background: var(--white);
    border-color: var(--black);
    color: var(--black);
}

[data-theme="dark"] .btn-secondary:hover {
    background: var(--black);
    color: var(--white);
}

/* 批量操作暗色主题样式已移除 */

[data-theme="dark"] .close-btn {
    background: var(--black);
    color: var(--white);
}

[data-theme="dark"] .close-btn:hover {
    background: var(--red);
}

[data-theme="dark"] .fab {
    border-color: var(--black);
}

[data-theme="dark"] .main-fab {
    background: var(--red);
    color: var(--white);
}

[data-theme="dark"] .sub-fab {
    background: var(--white);
    color: var(--black);
    border-color: var(--black);
}

[data-theme="dark"] .sub-fab:hover {
    background: var(--black);
    color: var(--white);
}

[data-theme="dark"] .shortcuts-hint {
    background: var(--black);
    color: var(--white);
}

[data-theme="dark"] .sidebar-overlay {
    background: rgba(255, 255, 255, 0.3);
}

/* 过期备忘录样式 */
.memo-card.overdue {
    border-left-color: #ff6b6b !important;
    background: linear-gradient(135deg, var(--white) 0%, #fff5f5 100%);
}

.memo-card.overdue .memo-meta {
    color: #ff6b6b;
}

.memo-card.overdue::before {
    content: '⚠️';
    position: absolute;
    top: calc(var(--grid) * 2);
    left: calc(var(--grid) * 6);
    font-size: 16px;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.memo-card {
    animation: slideInUp 0.3s ease;
}

.modal.active {
    animation: fadeIn 0.3s ease;
}

.stats-card {
    animation: slideInUp 0.3s ease;
    animation-delay: calc(var(--animation-delay, 0) * 0.1s);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: calc(var(--grid) * 2);
    }

    h1 {
        font-size: 32px;
    }

    .search-input {
        width: 150px;
    }

    .stats-dashboard {
        grid-template-columns: repeat(2, 1fr);
        gap: calc(var(--grid) * 2);
        padding: calc(var(--grid) * 2);
    }

    .toolbar {
        grid-template-columns: 1fr;
        gap: calc(var(--grid) * 2);
        text-align: center;
    }

    .toolbar-left, .toolbar-center, .toolbar-right {
        justify-content: center;
        flex-wrap: wrap;
    }

    .filter-buttons {
        overflow-x: auto;
        padding-bottom: var(--grid);
    }

    .memo-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        padding: calc(var(--grid) * 2);
        margin: calc(var(--grid) * 2);
    }

    /* 分享选项已移除 */

    .sidebar {
        width: 100%;
        max-width: 280px;
    }

    .fab-container {
        bottom: 20px;
        right: 20px;
    }

    .shortcuts-hint {
        bottom: 20px;
        left: 20px;
        font-size: 10px;
        padding: calc(var(--grid) * 2);
    }

    .calendar-header {
        padding: calc(var(--grid) * 2);
    }

    .calendar-day {
        min-height: 60px;
        padding: calc(var(--grid));
    }

    /* 批量操作响应式样式已移除 */

    header {
        grid-template-columns: 1fr;
        text-align: center;
        gap: calc(var(--grid) * 2);
    }
}
